/**
 * Sistema de Fallbacks Inteligentes
 * Gera conteúdo de alta qualidade quando a API OpenAI falha
 */

import { 
  generateSlug, 
  detectProductGender, 
  correctSpelling, 
  correctGrammarAndFormatting, 
  standardizeTitle,
  type SeoContent,
  type ProductFormData 
} from './seoGenerator';

/**
 * Templates inteligentes por categoria de produto
 */
const CATEGORY_TEMPLATES = {
  'eletrónica': {
    intro: [
      'Este produto eletrónico representa a escolha ideal para quem procura tecnologia avançada e funcionalidade.',
      'Desenvolvido com tecnologia de ponta, este dispositivo oferece desempenho excecional.',
      'A inovação tecnológica encontra a praticidade neste produto eletrónico de qualidade superior.'
    ],
    benefits: [
      'tecnologia avançada para máximo desempenho',
      'design moderno e funcional',
      'facilidade de utilização intuitiva',
      'durabilidade e fiabilidade comprovadas'
    ],
    usage: [
      'Ideal para uso doméstico e profissional, adaptando-se às suas necessidades diárias.',
      'Perfeito para quem valoriza eficiência e qualidade tecnológica.',
      'Adequa-se a diversos ambientes, oferecendo versatilidade de utilização.'
    ]
  },
  'vestuário': {
    intro: [
      'Esta peça de vestuário combina estilo e conforto numa solução perfeita.',
      'Desenvolvida para quem não abdica de qualidade e elegância.',
      'O design cuidado encontra a funcionalidade nesta peça excecional.'
    ],
    benefits: [
      'materiais de qualidade superior',
      'conforto excecional durante todo o dia',
      'design moderno e versátil',
      'durabilidade e resistência'
    ],
    usage: [
      'Perfeita para diversas ocasiões, desde o casual ao mais formal.',
      'Adapta-se ao seu estilo pessoal com elegância natural.',
      'Ideal para quem procura versatilidade e qualidade no vestuário.'
    ]
  },
  'casa': {
    intro: [
      'Este produto para casa transforma o seu espaço com funcionalidade e estilo.',
      'Desenvolvido para valorizar o seu lar com qualidade e design.',
      'A praticidade encontra a elegância nesta solução para casa.'
    ],
    benefits: [
      'design elegante que valoriza qualquer ambiente',
      'funcionalidade prática para o dia a dia',
      'materiais duráveis e de qualidade',
      'fácil manutenção e utilização'
    ],
    usage: [
      'Integra-se perfeitamente em qualquer decoração, valorizando o ambiente.',
      'Ideal para quem procura soluções práticas e elegantes para casa.',
      'Adequa-se a diferentes estilos de decoração com naturalidade.'
    ]
  },
  'desporto': {
    intro: [
      'Este equipamento desportivo oferece performance e qualidade para os seus treinos.',
      'Desenvolvido para atletas que procuram excelência e resultados.',
      'A tecnologia desportiva encontra a funcionalidade neste produto superior.'
    ],
    benefits: [
      'performance otimizada para melhores resultados',
      'materiais técnicos de alta qualidade',
      'design ergonómico e confortável',
      'durabilidade para uso intensivo'
    ],
    usage: [
      'Perfeito para treinos intensivos e atividades desportivas regulares.',
      'Adapta-se às suas necessidades desportivas com eficiência.',
      'Ideal para quem leva o desporto a sério e procura qualidade.'
    ]
  },
  'beleza': {
    intro: [
      'Este produto de beleza oferece cuidado profissional e resultados visíveis.',
      'Desenvolvido com ingredientes cuidadosamente selecionados para máxima eficácia.',
      'A ciência da beleza encontra a natureza nesta fórmula excecional.'
    ],
    benefits: [
      'fórmula avançada com ingredientes de qualidade',
      'resultados visíveis e duradouros',
      'adequado para uso diário',
      'testado dermatologicamente'
    ],
    usage: [
      'Integra-se perfeitamente na sua rotina de cuidados diários.',
      'Ideal para quem procura resultados profissionais em casa.',
      'Adequa-se a diferentes tipos de pele com eficácia comprovada.'
    ]
  }
};

/**
 * Gerador de fallback inteligente para quando a API falha
 */
export function generateIntelligentFallback(productData: ProductFormData): SeoContent {
  const productName = standardizeTitle(productData.name);
  const category = detectCategory(productData.category || '');
  const gender = detectProductGender(productName);
  const features = productData.features?.filter(f => f.trim()).map(f => correctSpelling(f.trim())) || [];
  const targetAudience = productData.targetAudience || 'quem procura qualidade e funcionalidade';
  const additionalInfo = productData.additionalInfo || '';

  // Selecionar template baseado na categoria
  const template = CATEGORY_TEMPLATES[category] || CATEGORY_TEMPLATES['casa'];
  
  // Gerar descrição principal
  const mainDescription = generateMainDescription(
    productName, 
    template, 
    features, 
    targetAudience, 
    additionalInfo,
    gender
  );

  // Gerar descrição curta
  const shortDescription = generateShortDescription(productName, features, targetAudience);

  // Gerar meta description SEO
  const seoDescription = generateSeoDescription(productName, category, features, targetAudience);

  // Aplicar correções finais
  return {
    wooCommerceMainDescription: correctGrammarAndFormatting(mainDescription),
    wooCommerceShortDescription: correctGrammarAndFormatting(shortDescription),
    shortDescription: correctGrammarAndFormatting(seoDescription),
    slug: generateSlug(productName)
  };
}

/**
 * Detecta categoria do produto baseada em palavras-chave
 */
function detectCategory(categoryInput: string): keyof typeof CATEGORY_TEMPLATES {
  const input = categoryInput.toLowerCase();
  
  if (input.includes('eletrónic') || input.includes('tecnolog') || input.includes('digital')) {
    return 'eletrónica';
  }
  if (input.includes('roupa') || input.includes('vestuário') || input.includes('moda')) {
    return 'vestuário';
  }
  if (input.includes('casa') || input.includes('lar') || input.includes('decoração')) {
    return 'casa';
  }
  if (input.includes('desporto') || input.includes('fitness') || input.includes('exercício')) {
    return 'desporto';
  }
  if (input.includes('beleza') || input.includes('cosmético') || input.includes('cuidado')) {
    return 'beleza';
  }
  
  return 'casa'; // Default
}

/**
 * Gera descrição principal usando template inteligente
 */
function generateMainDescription(
  productName: string,
  template: any,
  features: string[],
  targetAudience: string,
  additionalInfo: string,
  gender: 'masculine' | 'feminine' | 'unknown'
): string {
  // Selecionar introdução aleatória
  const intro = template.intro[Math.floor(Math.random() * template.intro.length)];
  
  // Adaptar introdução ao género do produto
  let adaptedIntro = intro.replace('Este produto', gender === 'feminine' ? 'Esta' : 'Este');
  adaptedIntro = adaptedIntro.replace('produto', gender === 'feminine' ? productName : productName);
  
  // Gerar parágrafo de características
  let featuresText = '';
  if (features.length > 0) {
    const enhancedFeatures = features.slice(0, 4).map(feature => enhanceFeature(feature));
    if (enhancedFeatures.length === 1) {
      featuresText = `Destaca-se pela sua ${enhancedFeatures[0]}.`;
    } else if (enhancedFeatures.length === 2) {
      featuresText = `Combina ${enhancedFeatures[0]} com ${enhancedFeatures[1]}.`;
    } else {
      const lastFeature = enhancedFeatures.pop();
      featuresText = `Integra ${enhancedFeatures.join(', ')} e ${lastFeature}.`;
    }
  } else {
    // Usar benefícios do template se não há características específicas
    const benefits = template.benefits.slice(0, 3);
    featuresText = `Oferece ${benefits.join(', ')}.`;
  }

  // Adicionar informações adicionais se fornecidas
  if (additionalInfo) {
    featuresText += ` ${additionalInfo}`;
  }

  // Selecionar texto de utilização
  const usage = template.usage[Math.floor(Math.random() * template.usage.length)];

  // Gerar call-to-action suave
  const callToAction = `Descubra como ${gender === 'feminine' ? 'a' : 'o'} ${productName} pode elevar a sua experiência e satisfazer as suas necessidades com qualidade superior.`;

  // Construir HTML final
  return `<p>${adaptedIntro}</p>

<p>${featuresText}</p>

<p>${usage}</p>

<p>${callToAction}</p>`;
}

/**
 * Melhora características transformando-as em benefícios
 */
function enhanceFeature(feature: string): string {
  const lowerFeature = feature.toLowerCase();
  
  const enhancements: { [key: string]: string } = {
    'resistente': 'resistência excecional',
    'durável': 'durabilidade superior',
    'leve': 'leveza e praticidade',
    'confortável': 'conforto excecional',
    'fácil': 'facilidade de utilização',
    'prático': 'funcionalidade prática',
    'elegante': 'elegância refinada',
    'moderno': 'design moderno',
    'versátil': 'versatilidade única',
    'eficiente': 'eficiência comprovada',
    'seguro': 'segurança garantida',
    'rápido': 'rapidez e agilidade'
  };

  for (const [key, enhancement] of Object.entries(enhancements)) {
    if (lowerFeature.includes(key)) {
      return enhancement;
    }
  }

  return `${feature} de qualidade`;
}

/**
 * Gera descrição curta para WooCommerce
 */
function generateShortDescription(productName: string, features: string[], targetAudience: string): string {
  const mainBenefit = features.length > 0 ? features[0] : 'qualidade superior';
  
  if (features.length > 1) {
    return `${productName} com ${mainBenefit} e ${features[1]}, ideal para ${targetAudience}.`;
  } else {
    return `${productName} com ${mainBenefit}, desenvolvido para ${targetAudience}.`;
  }
}

/**
 * Smart truncation for SEO descriptions that respects word boundaries
 */
function smartTruncateForSeo(text: string, maxLength: number): string {
  if (!text || text.length <= maxLength) {
    return text;
  }

  // Find the last complete word that fits
  let truncated = text.substring(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(' ');

  // If we found a space and it's not too close to the beginning
  if (lastSpaceIndex > maxLength * 0.7) {
    truncated = truncated.substring(0, lastSpaceIndex);
  } else {
    // Look for punctuation marks
    const lastPunctuationIndex = Math.max(
      truncated.lastIndexOf('.'),
      truncated.lastIndexOf(','),
      truncated.lastIndexOf('!')
    );

    if (lastPunctuationIndex > maxLength * 0.7) {
      truncated = truncated.substring(0, lastPunctuationIndex + 1);
    } else {
      // As last resort, find word boundaries
      const words = text.split(' ');
      let result = '';

      for (const word of words) {
        if ((result + ' ' + word).length > maxLength - 3) {
          break;
        }
        result += (result ? ' ' : '') + word;
      }

      truncated = result || text.substring(0, maxLength - 3);
    }
  }

  // Clean up and add ellipsis if needed
  truncated = truncated.trim();

  if (truncated.length < text.trim().length) {
    if (truncated.length > maxLength - 3) {
      truncated = truncated.substring(0, maxLength - 3);
    }
    truncated += '...';
  }

  return truncated;
}

/**
 * Gera meta description otimizada para SEO
 */
function generateSeoDescription(productName: string, category: string, features: string[], targetAudience: string): string {
  let description = `${productName}`;
  
  if (features.length > 0) {
    description += ` com ${features[0]}`;
  }
  
  description += ` para ${targetAudience}`;
  
  // Adicionar final otimizado para SEO
  const seoEndings = [
    '. Qualidade premium garantida',
    '. Entrega rápida em Portugal',
    '. Melhor preço e qualidade',
    '. Produto de confiança'
  ];
  
  for (const ending of seoEndings) {
    const testDescription = description + ending;
    if (testDescription.length >= 140 && testDescription.length <= 160) {
      return testDescription;
    }
  }
  
  // Se nenhum ending funcionar, ajustar manualmente
  if (description.length < 140) {
    description += '. Produto de qualidade superior com garantia de satisfação';
  }
  
  if (description.length > 160) {
    description = smartTruncateForSeo(description, 160);
  }
  
  return description;
}

/**
 * Fallback para melhoria de conteúdo existente
 */
export function generateImprovementFallback(currentDescription: string, productName?: string): SeoContent {
  const cleanDescription = currentDescription.replace(/<[^>]*>/g, '').trim();
  const name = standardizeTitle(productName || 'Produto Melhorado');
  
  // Extrair palavras-chave do conteúdo existente
  const words = cleanDescription.split(/\s+/).filter(word => word.length > 4);
  const keyFeatures = words.slice(0, 3);
  
  // Gerar versão melhorada
  const improvedDescription = `<p><strong>${name} - Versão Otimizada</strong></p>

<p>Esta versão melhorada incorpora todas as qualidades do produto original com aprimoramentos significativos. ${cleanDescription.substring(0, 200)}...</p>

<p>As melhorias implementadas incluem maior durabilidade, design aprimorado e funcionalidade otimizada, garantindo uma experiência superior.</p>

<p>Escolha a qualidade melhorada e sinta a diferença que um produto verdadeiramente otimizado pode fazer.</p>`;

  const shortDescription = `${name} melhorado com qualidade superior e funcionalidades aprimoradas.`;
  
  const seoDescription = generateSeoDescription(name, 'melhorado', keyFeatures, 'quem procura qualidade superior');

  return {
    wooCommerceMainDescription: correctGrammarAndFormatting(improvedDescription),
    wooCommerceShortDescription: correctGrammarAndFormatting(shortDescription),
    shortDescription: correctGrammarAndFormatting(seoDescription),
    slug: generateSlug(name)
  };
}
